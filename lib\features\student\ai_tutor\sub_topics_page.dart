import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'language_selector.dart';
import 'dart:math' as math;

class ParticlePainter extends CustomPainter {
  final double progress;

  ParticlePainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.shade200.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final random = math.Random();
    for (int i = 0; i < 50; i++) {
      double x = random.nextDouble() * size.width;
      double y = random.nextDouble() * size.height;
      double radius = random.nextDouble() * 2 + 1;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class SubTopicsPage extends StatelessWidget {
  final String topicId;
  final String topicName;
  final String subjectName;

  const SubTopicsPage({
    Key? key,
    required this.topicId,
    required this.topicName,
    required this.subjectName,
  }) : super(key: key);

  Future<List<dynamic>> fetchSubTopics() async {
    final url = "https://sasthra.in/api/subtopics/$topicId";
    debugPrint("📡 Fetching subtopics from: $url");

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data is List) {
          debugPrint("✅ SubTopics fetched successfully (${data.length})");
          return data;
        } else {
          debugPrint("❌ Invalid data format: Expected a list");
          return [];
        }
      } else {
        debugPrint("❌ Failed to load subtopics: ${response.body}");
        return [];
      }
    } catch (e) {
      debugPrint("⚠️ Exception fetching subtopics: $e");
      return [];
    }
  }

  Widget buildSubTopicCard(BuildContext context, Map<String, dynamic> subTopic, int index) {
    final subTopicId = subTopic["sub_topic_id"].toString();
    final subTopicName = subTopic["sub_topic_name"] ?? "Unnamed SubTopic";
    // Mock mastery percentage (replace with actual API data if available)
    final masteryPercentage = subTopic["mastery_percentage"] ?? 0.0;

    return TweenAnimationBuilder(
      duration: const Duration(milliseconds: 400 + (index * 100)),
      curve: Curves.easeOutBack,
      tween: Tween<double>(begin: 0, end: 1),
      builder: (context, value, child) {
        final clampedValue = value.clamp(0.0, 1.0); // Ensure opacity is within 0.0 to 1.0
        return Opacity(
          opacity: clampedValue,
          child: Transform.scale(
            scale: clampedValue,
            child: child,
          ),
        );
      },
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
          padding: const EdgeInsets.all(12),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(12)),
            boxShadow: [
              BoxShadow(
                color: Color(0x33000000), // Grey with 20% opacity
                spreadRadius: 2,
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.book, size: 20, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        subTopicName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: const BoxDecoration(
                      color: Color(0x1AFFA500), // Green shade 100
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    child: const Icon(Icons.check_circle, size: 16, color: Colors.green),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: masteryPercentage / 100,
                backgroundColor: Colors.grey.shade200,
                color: Colors.blue,
                minHeight: 8,
                borderRadius: const BorderRadius.all(Radius.circular(4)),
              ),
              const SizedBox(height: 4),
              Text(
                "${(masteryPercentage).toStringAsFixed(0)}% mastered",
                style: const TextStyle(fontSize: 12, color: Color(0x99000000)), // Grey 600
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      debugPrint("➡️ Quiz tapped: $subTopicName");
                      // Add quiz navigation or functionality here
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade50,
                      foregroundColor: Colors.blue,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.quiz, size: 16),
                        SizedBox(width: 4),
                        Text("Quiz", style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      debugPrint("➡️ Notes tapped: $subTopicName");
                      // Add notes navigation or functionality here
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.yellow.shade100,
                      foregroundColor: Colors.orange,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.notes, size: 16),
                        SizedBox(width: 4),
                        Text("Notes", style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          CustomPaint(
            painter: ParticlePainter(1.0),
            child: const SizedBox(),
          ),
          FutureBuilder<List<dynamic>>(
            future: fetchSubTopics(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: Colors.blue,
                  ),
                );
              }
              if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                return const Center(
                  child: Text(
                    "No subtopics found",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.black54),
                  ),
                );
              }

              final subTopics = snapshot.data!;
              return Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const SizedBox(height: 40),
                          Text(
                            topicName.toUpperCase(),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1,
                              fontSize: 24,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            "${subTopics.length} learning modules",
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0x99000000), // Grey 600
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(8.0),
                        itemCount: subTopics.length,
                        itemBuilder: (context, index) => buildSubTopicCard(context, subTopics[index], index),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}